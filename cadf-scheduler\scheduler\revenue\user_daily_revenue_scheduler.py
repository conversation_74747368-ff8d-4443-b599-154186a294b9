import time
from datetime import datetime, date
from typing import List, Dict

from models.models import AccountTrafficMetricsSnapshot, UserDailyRevenue
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler


def calculate_daily_revenue(yesterday_snapshot: AccountTrafficMetricsSnapshot,
                          previous_snapshots: List[AccountTrafficMetricsSnapshot]) -> int:
    """计算每日收益"""
    yesterday_views = yesterday_snapshot.view_count or 0

    base_views = 0
    if previous_snapshots:
        base_views = previous_snapshots[0].view_count or 0

    incremental_views = max(0, yesterday_views - base_views)
    return int(incremental_views * 0.1)


async def execute_task() -> None:
    """计算用户每日任务收益"""
    olog.info("开始执行用户每日任务收益计算")

    today = date.today()
    today_timestamp = int(datetime.combine(today, datetime.min.time()).timestamp())
    yesterday_timestamp = today_timestamp - 86400

    olog.debug(f"时间戳范围: {yesterday_timestamp} - {today_timestamp}")

    yesterday_snapshots = await AccountTrafficMetricsSnapshot.find(
        AccountTrafficMetricsSnapshot.snapshot_at >= yesterday_timestamp,
        AccountTrafficMetricsSnapshot.snapshot_at < today_timestamp
    ).to_list()

    olog.debug(f"查询到昨天快照数据: {len(yesterday_snapshots)} 条")

    yesterday_data: Dict[tuple, AccountTrafficMetricsSnapshot] = {}
    for snapshot in yesterday_snapshots:
        key = (snapshot.user_id, snapshot.promotion_task_detail_id)
        yesterday_data[key] = snapshot

    olog.debug(f"昨天有效快照数据: {len(yesterday_data)} 条")

    processed_count = 0
    total_revenue = 0

    for (user_id, task_detail_id), yesterday_snapshot in yesterday_data.items():
        previous_snapshots = await AccountTrafficMetricsSnapshot.find(
            AccountTrafficMetricsSnapshot.user_id == user_id,
            AccountTrafficMetricsSnapshot.promotion_task_detail_id == task_detail_id,
            AccountTrafficMetricsSnapshot.snapshot_at < yesterday_timestamp
        ).sort([("snapshot_at", -1)]).limit(1).to_list()

        daily_revenue = calculate_daily_revenue(yesterday_snapshot, previous_snapshots)

        if daily_revenue <= 0:
            continue

        existing_revenue = await UserDailyRevenue.find_one(
            UserDailyRevenue.user_id == user_id,
            UserDailyRevenue.promotion_task_detail_id == task_detail_id,
            UserDailyRevenue.date == yesterday_timestamp
        )

        current_time = int(time.time())

        if existing_revenue:
            await existing_revenue.update({
                "$set": {
                    "daily_revenue": daily_revenue,
                    "status": "未结算",
                    "settled_at": None
                }
            })
        else:
            new_revenue = UserDailyRevenue(
                user_id=user_id,
                promotion_task_detail_id=task_detail_id,
                date=yesterday_timestamp,
                daily_revenue=daily_revenue,
                status="未结算",
                settled_at=None,
                created_at=current_time
            )
            await new_revenue.insert()

        processed_count += 1
        total_revenue += daily_revenue

    olog.info(f"用户每日任务收益计算完成，处理了 {processed_count} 条记录，总收益: {total_revenue}分")


@register_scheduler(trigger="cron", hour="7", minute="0")
class UserDailyRevenueScheduler(BaseScheduler):
    async def run_task(self) -> None:
        """
        执行每日任务收益计算任务
        
        调度频率：每天早上7点执行一次
        """
        olog.info("开始执行每日任务收益计算任务")
        await execute_task()
        olog.info("每日任务收益计算任务执行完毕")


if __name__ == "__main__":
    import asyncio


    async def main():
        await init_models()
        await execute_task()


    asyncio.run(main())
